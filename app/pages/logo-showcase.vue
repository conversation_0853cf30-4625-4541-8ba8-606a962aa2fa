<script lang="ts" setup>
import { ref, onMounted } from 'vue'

// Meta data cho page
useHead({
  title: 'Logo Showcase - Imagen AI',
  meta: [
    { name: 'description', content: 'Showcase of the Imagen AI logo with various sizes and animations' }
  ]
})

// State management
const isLoading = ref(false)
const currentScale = ref(5)
const showControls = ref(true)

// Predefined scales
const scaleOptions = [
  { label: '2x', value: 2 },
  { label: '3x', value: 3 },
  { label: '5x', value: 5 },
  { label: '8x', value: 8 },
  { label: '10x', value: 10 }
]

// Functions
const toggleLoading = () => {
  isLoading.value = !isLoading.value
}

const setScale = (scale: number) => {
  currentScale.value = scale
}

const toggleControls = () => {
  showControls.value = !showControls.value
}

// Auto-hide controls after 5 seconds
let hideControlsTimer: NodeJS.Timeout
const resetHideTimer = () => {
  if (hideControlsTimer) {
    clearTimeout(hideControlsTimer)
  }
  showControls.value = true
  hideControlsTimer = setTimeout(() => {
    showControls.value = false
  }, 5000)
}

onMounted(() => {
  resetHideTimer()
  
  // Add mouse move listener to show controls
  document.addEventListener('mousemove', resetHideTimer)
  
  // Cleanup on unmount
  onUnmounted(() => {
    if (hideControlsTimer) {
      clearTimeout(hideControlsTimer)
    }
    document.removeEventListener('mousemove', resetHideTimer)
  })
})
</script>

<template>
  <div class="logo-showcase-page">
    <!-- Main Logo Display -->
    <BaseLargeLogo
      :loading="isLoading"
      :scale="currentScale"
    />

    <!-- Controls Panel -->
    <Transition name="fade">
      <div
        v-show="showControls"
        class="controls-panel"
      >
        <!-- Loading Toggle -->
        <div class="control-group">
          <UButton
            :color="isLoading ? 'red' : 'primary'"
            :variant="isLoading ? 'solid' : 'outline'"
            size="lg"
            @click="toggleLoading"
          >
            {{ isLoading ? 'Stop Loading' : 'Start Loading' }}
          </UButton>
        </div>

        <!-- Scale Controls -->
        <div class="control-group">
          <label class="control-label">Scale:</label>
          <div class="scale-buttons">
            <UButton
              v-for="option in scaleOptions"
              :key="option.value"
              :color="currentScale === option.value ? 'primary' : 'gray'"
              :variant="currentScale === option.value ? 'solid' : 'outline'"
              size="sm"
              @click="setScale(option.value)"
            >
              {{ option.label }}
            </UButton>
          </div>
        </div>

        <!-- Custom Scale Input -->
        <div class="control-group">
          <label class="control-label">Custom Scale:</label>
          <UInput
            v-model.number="currentScale"
            type="number"
            min="1"
            max="20"
            step="0.5"
            class="scale-input"
          />
        </div>

        <!-- Hide Controls Button -->
        <div class="control-group">
          <UButton
            color="gray"
            variant="ghost"
            size="sm"
            @click="toggleControls"
          >
            Hide Controls (Auto-hide in 5s)
          </UButton>
        </div>
      </div>
    </Transition>

    <!-- Show Controls Button (when hidden) -->
    <Transition name="fade">
      <UButton
        v-show="!showControls"
        class="show-controls-btn"
        color="primary"
        variant="outline"
        size="sm"
        @click="toggleControls"
      >
        Show Controls
      </UButton>
    </Transition>
  </div>
</template>

<style lang="scss" scoped>
.logo-showcase-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(15, 16, 35, 1) 0%,
    rgba(35, 52, 93, 0.95) 25%,
    rgba(146, 22, 100, 0.95) 75%,
    rgba(15, 16, 35, 1) 100%
  );
}

.controls-panel {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 250px;
}

.control-group {
  margin-bottom: 1rem;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.control-label {
  display: block;
  color: white;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.scale-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.scale-input {
  width: 100%;
  max-width: 120px;
}

.show-controls-btn {
  position: fixed;
  top: 2rem;
  right: 2rem;
  z-index: 1000;
}

/* Transitions */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .controls-panel {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    padding: 1rem;
    min-width: auto;
  }
  
  .show-controls-btn {
    top: 1rem;
    right: 1rem;
  }
  
  .scale-buttons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .controls-panel {
    padding: 0.75rem;
  }
  
  .scale-buttons {
    gap: 0.25rem;
  }
}
</style>
