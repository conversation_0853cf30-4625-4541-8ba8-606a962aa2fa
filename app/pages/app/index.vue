<script setup lang="ts">
import { compareImageArrays, commonValidationRules } from '~/utils/generationValidation'

interface ImageFile {
  src: string
  alt: string
  file: File
}
const authStore = useAuthStore()
const { isAuthenticated, user_credit } = storeToRefs(authStore)
const { model, models } = useImageGenModels()
const { style } = useStyles()
const { imageDimension } = useImageDimensions()
const router = useRouter()
const toast = useToast()
const { handleGeneration } = useGenerationConfirmation()
const { t } = useI18n()

const aiPhotos = [
  'https://cdn.leonardo.ai/users/c593f6db-e19b-43f4-b0ce-f5757ff82907/generations/27ceb2db-a6c1-4533-a1bc-35375113bf45/segments/5:8:1/Leonardo_Phoenix_10_A_Clean_Shaven_Strangely_Odd_Unorthodox_Av_0.jpg?w=512',
  'https://cdn.leonardo.ai/users/daeb794a-c999-4c7c-a7bd-1b22321efa4e/generations/1e0d2e32-d948-47c3-891a-7ea47ce900d8/Leonardo_Phoenix_10_HD_animestyle_couple_walking_along_a_narro_1.jpg?w=512',
  'https://cdn.leonardo.ai/users/dae3edd9-f7aa-4aef-adc1-63cb53d7d8b8/generations/d2236476-d02a-4d4c-8369-b1671b64ca6b/variations/alchemyrefiner_alchemymagic_3_d2236476-d02a-4d4c-8369-b1671b64ca6b_0.jpg?w=512',
  'https://cdn.leonardo.ai/users/818a38be-6136-4eba-9b0e-9ec156e41811/generations/b0ec8580-6a5a-4e6c-a203-cd7dfe27b9b1/Leonardo_Phoenix_10_A_large_reddishgolden_colored_cartoon_cat_3.jpg?w=512',
  'https://cdn.leonardo.ai/users/684d2cf2-484a-44d8-bf86-4fac5fe47a59/generations/78afd409-dccb-4508-b4bc-5c2b625171e9/Leonardo_Phoenix_10_A_pair_of_enchanting_fantasy_birds_perched_0.jpg?w=512'
]

const textToImageStore = useTextToImageStore()
const { textToImageResult, aiToolImageCardRef, prompt, loadings, errors }
  = storeToRefs(textToImageStore)

// Local state for selected images
const selectedImages = ref<ImageFile[]>([])

// Local state for person generation and safety filter
const personGeneration = ref('DONT_ALLOW')
const safetyFilterLevel = ref('BLOCK_LOW_AND_ABOVE')

// Store initial values to compare for changes
const initialValues = ref({
  prompt: '',
  model: models[0],
  style: 'Dynamic',
  imageDimension: '1:1',
  personGeneration: 'DONT_ALLOW',
  safetyFilterLevel: 'BLOCK_LOW_AND_ABOVE',
  selectedImages: [] as ImageFile[]
})

// Initialize initial values on mount
onMounted(() => {
  initialValues.value = {
    prompt: prompt.value,
    model: model.value,
    style: style.value,
    imageDimension: imageDimension.value,
    personGeneration: personGeneration.value,
    safetyFilterLevel: safetyFilterLevel.value,
    selectedImages: [...selectedImages.value]
  }
})

// Check if any values have changed from initial state
const hasChanges = computed(() => {
  // Basic field comparisons
  const basicFieldsChanged = (
    prompt.value !== initialValues.value.prompt
    || model.value?.value !== initialValues.value.model?.value
    || style.value !== initialValues.value.style
    || imageDimension.value !== initialValues.value.imageDimension
    || personGeneration.value !== initialValues.value.personGeneration
    || safetyFilterLevel.value !== initialValues.value.safetyFilterLevel
  )

  // Image comparison with better performance
  const imagesChanged = compareImageArrays(selectedImages.value, initialValues.value.selectedImages)

  return basicFieldsChanged || imagesChanged
})

// Handle image selection
const handleImagesSelected = (images: ImageFile[]) => {
  selectedImages.value = images
  // Also update store for backward compatibility
  textToImageStore.selectedImages = images
}

// Helper function to perform the actual generation
const performGeneration = async () => {
  // Extract File objects from selected images
  const files = selectedImages.value.map(img => img.file).filter(Boolean)

  const result = await textToImageStore.textToImage({
    prompt: prompt.value,
    model: model.value?.value || 'gemini-2.0-flash-exp-image-generation',
    style: style.value || 'Portrait',
    aspect_ratio: imageDimension.value || '1:1',
    person_generation: personGeneration.value,
    safety_filter_level: safetyFilterLevel.value,
    files: files
  })

  if (result) {
    toast.add({
      id: 'success',
      title: 'Image Generation',
      description: 'Your image is ready!',
      color: 'success'
    })

    // Update initial values after successful generation
    initialValues.value = {
      prompt: prompt.value,
      model: model.value,
      style: style.value,
      imageDimension: imageDimension.value,
      personGeneration: personGeneration.value,
      safetyFilterLevel: safetyFilterLevel.value,
      selectedImages: [...selectedImages.value]
    }
  }
}

const onGenerate = async () => {
  if (!isAuthenticated.value) {
    router.push('/auth/login')
    return
  }

  // Define validation rules
  const validationRules = [
    commonValidationRules.requiredText(
      prompt.value,
      t('Please enter a prompt to generate an image.')
    )
  ]

  // Use the unified generation confirmation logic
  await handleGeneration({
    generationType: 'image',
    hasChanges,
    hasResult: computed(() => !!textToImageResult.value),
    onGenerate: performGeneration,
    validationRules
  })
}
</script>

<template>
  <UContainer class="mt-0">
    <div
      class="grid grid-cols-1 lg:grid-cols-2 sm:gap-4 lg:gap-6 space-y-8 sm:space-y-0"
    >
      <UCard>
        <div class="flex flex-col gap-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField :label="$t('model')">
              <BaseModelSelect
                v-model="model"
                :models="models"
                class="w-full"
              />
            </UFormField>
            <UFormField
              v-if="model?.options?.includes('style')"
              :label="$t('style')"
            >
              <BaseImageStyleSelect
                v-model="style"
                class="w-full"
                size="sm"
              />
            </UFormField>
          </div>
          <UFormField :label="$t('Prompt')">
            <UTextarea
              v-model="prompt"
              class="w-full"
              :placeholder="$t('Describe the image you want to generate...')"
              :rows="6"
            />
          </UFormField>
          <UFormField
            v-if="model?.options?.includes('imageDimensions')"
            :label="$t('aspectRatio')"
          >
            <BaseImageDimensionsSelect />
          </UFormField>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField
              v-if="model?.options?.includes('personGeneration')"
              :label="$t('Person Generation')"
            >
              <BasePersonGenerationSelect
                v-model="personGeneration"
                class="w-full"
              />
            </UFormField>
            <UFormField
              v-if="model?.options?.includes('safetyFilterLevel')"
              :label="$t('Safety Filter')"
            >
              <BaseSafetyFilterSelect
                v-model="safetyFilterLevel"
                class="w-full"
              />
            </UFormField>
          </div>
          <div
            v-if="model?.options?.includes('yourImage')"
            class="flex flex-row gap-3 items-end"
          >
            <UFormField :label="$t('Image Reference')">
              <BaseImageSelect
                v-model="selectedImages"
                @update:model-value="handleImagesSelected"
              />
            </UFormField>
            <BaseImageSelectedList
              v-model="selectedImages"
              @update:model-value="handleImagesSelected"
            />
          </div>
          <div class="flex justify-end gap-2 items-center flex-row">
            <div class="text-xs text-right">
              <div>
                {{
                  $t("Credits: {credits} remaining", {
                    credits: formatNumber(user_credit?.available_credit || 0)
                  })
                }}
              </div>
              <div class="text-primary">
                {{
                  $t("This generation will cost: {cost} Credits", {
                    cost: 50
                  })
                }}
              </div>
            </div>
            <UButton
              color="primary"
              :label="$t('generate')"
              class="bg-gradient-to-r from-primary-500 to-primary-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-secondary-600 cursor-pointer"
              trailing-icon="line-md:arrow-right"
              :loading="loadings['textToImage']"
              :disabled="!prompt"
              @click="onGenerate"
            />
          </div>
        </div>
      </UCard>
      <Motion
        v-if="(textToImageResult || loadings['textToImage']) && !errors['textToImage']"
        ref="aiToolImageCardRef"
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.6,
          delay: 0.5
        }"
      >
        <AIToolImageCard
          v-bind="textToImageResult"
          :data="textToImageResult"
          :loading="loadings['textToImage']"
          class="h-full"
        />
      </Motion>
      <UCard
        v-else
        :ui="{
          body: 'h-full dark:text-muted/40'
        }"
      >
        <div class="flex flex-col items-center justify-center h-full">
          <div>
            <UIcon
              :name="errors['textToImage'] ? 'i-lucide-alert-circle' : 'i-lucide-image'"
              class="text-6xl mb-2"
              :class="errors['textToImage'] ? 'text-error' : ''"
            />
          </div>
          <div
            v-if="errors['textToImage']"
            class="text-sm text-error"
          >
            {{ errors['textToImage'] }}
          </div>
          <div
            v-else
            class="text-sm"
          >
            {{ $t('Your generated image will appear here') }}
          </div>
        </div>
      </UCard>
    </div>
    <Motion
      :initial="{
        scale: 1.1,
        opacity: 0,
        filter: 'blur(20px)'
      }"
      :animate="{
        scale: 1,
        opacity: 1,
        filter: 'blur(0px)'
      }"
      :transition="{
        duration: 0.5,
        delay: 1
      }"
    >
      <UPageMarquee
        pause-on-hover
        class="py-2 -mx-4 sm:-mx-6 lg:-mx-8 [--duration:40s] mt-6"
      >
        <Motion
          v-for="(img, index) in aiPhotos"
          :key="index"
          :initial="{
            scale: 1.1,
            opacity: 0,
            filter: 'blur(20px)'
          }"
          :animate="{
            scale: 1,
            opacity: 1,
            filter: 'blur(0px)'
          }"
          :transition="{
            duration: 0.6,
            delay: index * 0.1
          }"
        >
          <img
            width="234"
            height="234"
            class="rounded-lg"
            :class="index % 2 === 0 ? '-rotate-2' : 'rotate-2'"
            :src="img"
          >
        </Motion>
      </UPageMarquee>
    </Motion>
  </UContainer>
</template>
