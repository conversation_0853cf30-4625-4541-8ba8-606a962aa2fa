<script setup lang="ts">
import { driver } from 'driver.js'
import { formatNumber } from '~/utils'
import {
  compareDialogArrays,
  commonValidationRules
} from '~/utils/generationValidation'

const authStore = useAuthStore()
const { isAuthenticated, user_credit } = storeToRefs(authStore)
const {
  model,
  models,
  speed,
  outputFormat,
  outputChannel,
  speedConfig,
  outputFormats,
  outputChannels
} = useSpeechGenModels()
const { loadVoices, selectedVoice, loading } = useSpeechVoices()
const { selectedEmotion } = useSpeechEmotions()
const router = useRouter()
const toast = useToast()
const { handleGeneration } = useGenerationConfirmation()
const { t } = useI18n()

const textToSpeechStore = useTextToSpeechStore()
const { prompt, selectedFiles, hasSelectedFiles }
  = storeToRefs(textToSpeechStore)

const dialogToSpeechStore = useDialogToSpeechStore()
const {
  dialogs,
  voice1,
  voice2,
  custom_prompt,
  loadings,
  dialogResult,
  errors
} = storeToRefs(dialogToSpeechStore)

// Store initial values to compare for changes
const initialValues = ref({
  custom_prompt: '',
  model: models[0],
  voice1: null,
  voice2: null,
  selectedEmotion: null,
  speed: 1.0,
  outputFormat: 'mp3',
  outputChannel: 'mono',
  dialogs: []
})

// Initialize initial values on mount
onMounted(() => {
  loadVoices()
  initialValues.value = {
    custom_prompt: custom_prompt.value,
    model: model.value,
    voice1: voice1.value,
    voice2: voice2.value,
    selectedEmotion: selectedEmotion.value,
    speed: speed.value,
    outputFormat: outputFormat.value,
    outputChannel: outputChannel.value,
    dialogs: JSON.parse(JSON.stringify(dialogs.value))
  }
})

// Check if any values have changed from initial state
const hasChanges = computed(() => {
  // Basic field comparisons
  const basicFieldsChanged
    = custom_prompt.value !== initialValues.value.custom_prompt
      || model.value?.value !== initialValues.value.model?.value
      || voice1.value?.id !== initialValues.value.voice1?.id
      || voice2.value?.id !== initialValues.value.voice2?.id
      || selectedEmotion.value?.emotion_key
      !== initialValues.value.selectedEmotion?.emotion_key
      || speed.value !== initialValues.value.speed
      || outputFormat.value !== initialValues.value.outputFormat
      || outputChannel.value !== initialValues.value.outputChannel

  // Optimized dialog comparison - avoid JSON.stringify for better performance
  const dialogsChanged = compareDialogArrays(
    dialogs.value,
    initialValues.value.dialogs
  )

  return basicFieldsChanged || dialogsChanged
})

// Helper function to perform the actual generation
const performGeneration = async () => {
  const result = await handleDialogToSpeech()

  if (result) {
    toast.add({
      id: 'success',
      title: 'Dialog Generation',
      description: 'Your dialog is being generated. Please check back later.',
      color: 'success'
    })

    // Update initial values after successful generation
    initialValues.value = {
      custom_prompt: custom_prompt.value,
      model: model.value,
      voice1: voice1.value,
      voice2: voice2.value,
      selectedEmotion: selectedEmotion.value,
      speed: speed.value,
      outputFormat: outputFormat.value,
      outputChannel: outputChannel.value,
      dialogs: JSON.parse(JSON.stringify(dialogs.value))
    }
  }
}

const onGenerate = async () => {
  if (!isAuthenticated.value) {
    router.push('/auth/login')
    return
  }

  // Define validation rules
  const validationRules = [
    commonValidationRules.requiredArray(
      dialogs.value,
      t('Please add at least one dialog to generate speech.')
    ),
    commonValidationRules.requiredMultiple(
      [voice1.value, voice2.value],
      t('Please select voices for both speakers.')
    )
  ]

  // Use the unified generation confirmation logic
  await handleGeneration({
    generationType: 'dialog',
    hasChanges,
    hasResult: computed(() => !!dialogResult.value),
    onGenerate: performGeneration,
    validationRules
  })
}

const handleDialogToSpeech = async () => {
  return await dialogToSpeechStore.generateDialogSpeech({
    model: model.value.value,
    emotion: selectedEmotion.value?.emotion_key,
    speed: speed.value,
    output_format: outputFormat.value,
    output_channel: outputChannel.value,
    custom_prompt: custom_prompt.value
  })
}

watch(
  () => hasSelectedFiles.value,
  (newValue) => {
    if (newValue) {
      prompt.value = t('Generate speech from selected file')
    } else {
      prompt.value = ''
      // focus to input
      nextTick(() => {
        const promptInput = document.querySelector(
          'textarea[placeholder*="dialog"]'
        )
        if (promptInput && promptInput instanceof HTMLElement) {
          promptInput.focus()
        }
      })
    }
  }
)

const showGuideSelectVoice = (hardShow = false) => {
  const driverObj = driver({
    showProgress: false,
    smoothScroll: true,
    showButtons: ['close', 'next'],
    nextBtnText: t('Next'),
    prevBtnText: t('Back'),
    doneBtnText: t('I got it!'),
    overlayClickBehavior: 'nextStep',
    stagePadding: 4,
    disableActiveInteraction: true,
    steps: [
      {
        element: '[data-tour="voices-library"]',
        popover: {
          title: t('Voices Library'),
          description: t('Select a voice for your speaker from the library.')
        }
      }
    ]
  })

  // check if guide is already shown
  if (localStorage.getItem('guide-select-voice') && !hardShow) return
  driverObj?.drive()
  localStorage.setItem('guide-select-voice', 'true')
}

const selectedSpeaker = ref(null) as Ref<'voice1' | 'voice2' | null>

const selectVoiceSuccess = ref(false)
const resetSuccessInstance = ref()
const onSelectVoice = (voice: SpeechVoice) => {
  if (selectedSpeaker.value === 'voice1') {
    dialogToSpeechStore.setVoice1(voice)
  } else if (selectedSpeaker.value === 'voice2') {
    dialogToSpeechStore.setVoice2(voice)
  }
  selectVoiceSuccess.value = true
  if (resetSuccessInstance.value) {
    clearTimeout(resetSuccessInstance.value)
  }
  resetSuccessInstance.value = setTimeout(() => {
    selectVoiceSuccess.value = false
  }, 2000)
}

const onSelectSpeaker = (speaker: 'voice1' | 'voice2') => {
  // check if user click again
  if (selectedSpeaker.value === speaker) {
    showGuideSelectVoice(true)
  }
  selectedSpeaker.value = speaker
}

watch(
  () => selectedSpeaker.value,
  (newValue) => {
    if (newValue === 'voice1') {
      selectedVoice.value = voice1.value
    } else if (newValue === 'voice2') {
      selectedVoice.value = voice2.value
    }

    if (newValue) {
      showGuideSelectVoice()
    }
  }
)

const outputFormatItems = computed(() => {
  return outputFormats.map(format => ({
    label: format.label,
    value: format.value
  }))
})
</script>

<template>
  <UContainer class="mt-0">
    <div
      class="grid grid-cols-1 lg:grid-cols-2 sm:gap-4 lg:gap-6 space-y-8 sm:space-y-0"
    >
      <UCard>
        <div class="flex flex-col gap-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField :label="$t('modelPreset')">
              <BaseModelSelect
                v-model="model"
                :models="models"
                class="w-full"
              />
            </UFormField>
            <UFormField
              v-if="model?.options?.includes('emotion')"
              :label="$t('emotion')"
            >
              <BaseSpeechEmotionSelectModal
                v-model="selectedEmotion"
                size="sm"
              />
            </UFormField>
          </div>

          <UFormField :label="$t('Style Description')">
            <UTextarea
              v-model="custom_prompt"
              class="w-full"
              :placeholder="
                $t(
                  'Describe the style of your dialog, e.g. *Read this in a dramatic whisper*'
                )
              "
              :rows="3"
            />
          </UFormField>

          <UFormField
            :label="$t('Voice')"
            class="w-full lg:hidden"
          >
            <UDrawer direction="right">
              <UButton
                :label="$t('Select Voices')"
                icon="lucide:users"
                color="neutral"
                variant="outline"
                trailing-icon="lucide:chevron-down"
                class="w-full"
                size="sm"
                :ui="{
                  trailingIcon: 'ml-auto'
                }"
              />

              <template #content>
                <VoicesLibraries full-screen />
              </template>
            </UDrawer>
          </UFormField>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <UFormField
              v-if="model?.options?.includes('voice')"
              :label="$t('voice 1')"
              :hint="
                selectedSpeaker === 'voice1' ? $t('Selecting a voice...') : ''
              "
            >
              <UButton
                :label="
                  voice1?.speaker_name || $t('Select Voice for speaker 1')
                "
                :icon="
                  selectVoiceSuccess && selectedSpeaker === 'voice1'
                    ? 'line-md:confirm-circle-twotone'
                    : 'ri:user-voice-line'
                "
                color="neutral"
                variant="outline"
                :trailing-icon="
                  selectedSpeaker === 'voice1'
                    ? 'svg-spinners:3-dots-fade'
                    : 'lucide:chevron-right'
                "
                class="w-full"
                size="sm"
                :ui="{
                  trailingIcon: 'ml-auto'
                }"
                :disabled="loading"
                @click="onSelectSpeaker('voice1')"
              />
            </UFormField>
            <UFormField
              v-if="model?.options?.includes('voice')"
              :label="$t('voice 2')"
              :hint="
                selectedSpeaker === 'voice2' ? $t('Selecting a voice...') : ''
              "
            >
              <UButton
                :label="
                  voice2?.speaker_name || $t('Select Voice for speaker 2')
                "
                :icon="
                  selectVoiceSuccess && selectedSpeaker === 'voice2'
                    ? 'line-md:confirm-circle-twotone'
                    : 'ri:user-voice-line'
                "
                color="neutral"
                variant="outline"
                :trailing-icon="
                  selectedSpeaker === 'voice2'
                    ? 'svg-spinners:3-dots-fade'
                    : 'lucide:chevron-right'
                "
                class="w-full"
                size="sm"
                :ui="{
                  trailingIcon: 'ml-auto'
                }"
                :disabled="loading"
                @click="onSelectSpeaker('voice2')"
              />
            </UFormField>
          </div>

          <div class="flex flex-row gap-6">
            <!-- Speed Settings -->
            <UFormField
              v-if="model?.options?.includes('speed')"
              :label="$t('speed')"
              class="flex-1"
            >
              <div class="flex flex-col gap-3">
                <UInputNumber
                  v-model="speed"
                  :min="speedConfig.min"
                  :max="speedConfig.max"
                  :step="speedConfig.step"
                  size="sm"
                  class="w-full"
                />
                <USlider
                  v-model="speed"
                  :min="speedConfig.min"
                  :max="speedConfig.max"
                  :step="speedConfig.step"
                  class="w-full"
                />
                <div class="flex justify-between text-xs text-gray-400">
                  <span>{{ speedConfig.min }}x</span>
                  <span>{{ speedConfig.max }}x</span>
                </div>
              </div>
            </UFormField>

            <!-- Output Format Settings -->
            <UFormField
              v-if="model?.options?.includes('outputFormat')"
              :label="$t('outputFormat')"
            >
              <URadioGroup
                v-model="outputFormat"
                orientation="vertical"
                variant="card"
                value-key="value"
                :items="outputFormatItems"
                size="xs"
              />
            </UFormField>
          </div>

          <UFormField :label="$t('Dialog Content')">
            <div class="flex flex-col gap-4">
              <BaseSpeakerGen
                v-for="(dialog, index) in dialogs"
                :key="index"
                :can-remove="index !== 0 || dialogs.length > 1"
                v-bind="dialog"
                @update:model-value="
                  dialogToSpeechStore.updateDialog(index, $event)
                "
                @remove="dialogToSpeechStore.removeDialog(index)"
              />
              <UButton
                color="neutral"
                variant="soft"
                icon="icons8:plus"
                :label="$t('Add dialog')"
                @click="dialogToSpeechStore.addDialog()"
              />
            </div>
          </UFormField>

          <div class="flex justify-end gap-2 items-center flex-row">
            <div class="text-xs text-right">
              <div>
                {{
                  $t("Credits: {credits} remaining", {
                    credits: formatNumber(user_credit?.available_credit || 0)
                  })
                }}
              </div>
              <div class="text-primary">
                {{
                  $t("This generation will cost: {cost} Credits", {
                    cost: 30
                  })
                }}
              </div>
            </div>
            <UButton
              color="primary"
              :label="$t('Generate')"
              class="bg-gradient-to-r from-primary-500 to-primary-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-success-600 cursor-pointer"
              trailing-icon="mingcute:ai-fill"
              :loading="loadings['generateSpeech']"
              :disabled="!dialogs.some((d) => d.input.trim())"
              @click="onGenerate"
            />
          </div>
        </div>
      </UCard>

      <Motion
        v-if="
          (dialogResult || loadings['generateSpeech'])
            && !errors['generateSpeech']
        "
        :initial="{
          scale: 1.1,
          opacity: 0,
          filter: 'blur(20px)'
        }"
        :animate="{
          scale: 1,
          opacity: 1,
          filter: 'blur(0px)'
        }"
        :transition="{
          duration: 0.6,
          delay: 0.5
        }"
      >
        <AIToolDialogCard
          v-bind="dialogResult"
          :data="dialogResult"
          :loading="loadings['generateSpeech']"
          class="h-full"
          @regenerate="onGenerate"
        />
      </Motion>
      <VoicesLibraries
        v-else
        data-tour="voices-library"
        class="hidden lg:block h-fit sticky top-20"
        @select-voice="onSelectVoice"
      />
      <UCard
        v-if="false"
        :ui="{
          body: 'h-full dark:text-muted/40'
        }"
      >
        <div class="flex flex-col items-center justify-center h-full">
          <div>
            <UIcon
              :name="
                errors['generateSpeech']
                  ? 'i-lucide-alert-circle'
                  : 'i-lucide-users'
              "
              class="text-6xl mb-2"
              :class="errors['generateSpeech'] ? 'text-error' : ''"
            />
          </div>
          <div
            v-if="errors['generateSpeech']"
            class="text-sm text-error"
          >
            {{ errors["generateSpeech"] }}
          </div>
          <div
            v-else
            class="text-sm"
          >
            {{ $t("Your generated dialog will appear here") }}
          </div>
        </div>
      </UCard>
    </div>

    <!-- Dialog Prompt Gallery -->
    <Motion
      :initial="{
        scale: 1.1,
        opacity: 0,
        filter: 'blur(20px)'
      }"
      :animate="{
        scale: 1,
        opacity: 1,
        filter: 'blur(0px)'
      }"
      :transition="{
        duration: 0.6,
        delay: 1.2
      }"
    >
      <AIToolDialogPromptGallery
        class="mt-8"
        @style-selected="
          (selectedStyle: string) => (custom_prompt = selectedStyle)
        "
        @dialog-selected="
          (selectedDialogs: Array<{ speaker: string; text: string }>) => {
            // Clear existing dialogs and add new ones
            dialogToSpeechStore.clearDialogs();
            selectedDialogs.forEach((dialog, index) => {
              if (index === 0) {
                // Update first dialog
                dialogToSpeechStore.updateDialog(0, {
                  speakerIndex: 0,
                  input: dialog.text
                });
              }
              else {
                // Add new dialogs
                dialogToSpeechStore.addDialog();
                dialogToSpeechStore.updateDialog(index, {
                  speakerIndex: index % 2,
                  input: dialog.text
                });
              }
            });
          }
        "
      />
    </Motion>
  </UContainer>
</template>
