<script setup lang="ts">
import { useNavLinks } from '~/utils/links'

// Use the new i18n-aware nav links composable
const translatedNavLinks = useNavLinks()
</script>

<template>
  <!-- <div class="">
    <div>
      <AppHeaderBanner />
      <AppHeader :links="translatedNavLinks" />
      <slot />
      <AppFooter />
    </div>
    <LazyLayoutStarBg />
    <LayoutWavesBg />
    <BaseLoadingOverlay />
    <NotificationDrawer />
    <HistoryDetailModal />
  </div> -->

  <div>
    <AppHeader :links="translatedNavLinks" />

    <UMain class="relative">
      <slot />
    </UMain>

    <AppFooter />
    <LazyLayoutStarBg />
    <BaseLoadingOverlay />
    <NotificationDrawer />
    <HistoryDetailModal />
    <BaseConfirmModal />
    <!-- <LayoutWavesBg /> -->
  </div>
</template>
