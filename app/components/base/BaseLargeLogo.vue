<script lang="ts" setup>
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  scale: {
    type: Number,
    default: 5 // Tăng kích thước lên 5 lần
  }
})

const logoStyle = computed(() => ({
  transform: `scale(${props.scale})`,
  transformOrigin: 'center center'
}))
</script>

<template>
  <div class="large-logo-container">
    <div
      class="logo-wrapper"
      :style="logoStyle"
    >
      <BaseLogo
        :loading="loading"
        size="normal"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.large-logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  padding: 2rem;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    rgba(15, 16, 35, 0.95) 0%,
    rgba(35, 52, 93, 0.9) 25%,
    rgba(146, 22, 100, 0.9) 75%,
    rgba(15, 16, 35, 0.95) 100%
  );
  position: relative;
}

.large-logo-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(142, 45, 226, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 30%,
    transparent 70%
  );
  z-index: 1;
}

.logo-wrapper {
  position: relative;
  z-index: 2;
  transition: transform 0.5s ease-in-out;
  filter: drop-shadow(0 0 50px rgba(142, 45, 226, 0.3));
}

.logo-wrapper:hover {
  filter: drop-shadow(0 0 80px rgba(142, 45, 226, 0.5));
}

/* Responsive scaling */
@media (max-width: 768px) {
  .large-logo-container {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .large-logo-container {
    padding: 0.5rem;
  }
}
</style>
