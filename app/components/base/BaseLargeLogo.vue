<script lang="ts" setup>
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  }
})
</script>

<template>
  <div
    class="circulate"
    :class="{ 'loading-active': loading }"
  >
    <div
      class="circle border-2 dark:border-gray-700 w-40 h-40"
      :class="{ 'loading-rotate': loading }"
    >
      <div
        class="wave _one"
        :class="{ 'loading-wave': loading }"
      />
      <div
        class="wave _two"
        :class="{ 'loading-wave': loading }"
      />
      <div
        class="wave _three"
        :class="{ 'loading-wave': loading }"
      />
      <div
        class="reflection"
        :class="{ 'loading-reflection': loading }"
      />

      <!-- Image elements being processed inside the ball -->
      <div
        class="image-element photo photo1"
        :class="{ 'loading-photo': loading }"
      />
      <div
        class="image-element photo photo2"
        :class="{ 'loading-photo': loading }"
      />
      <div
        class="image-element photo photo3"
        :class="{ 'loading-photo': loading }"
      />
      <div
        class="image-element photo photo4"
        :class="{ 'loading-photo': loading }"
      />

      <!-- Particles to enhance the processing effect -->
      <div
        class="particles"
        :class="{ 'loading-particles': loading }"
      >
        <div
          v-for="n in 8"
          :key="n"
          class="particle"
          :class="[`p${n}`, { 'loading-particle': loading }]"
        />
      </div>

      <!-- Energy glow effect -->
      <div
        class="energy-glow"
        :class="{ 'loading-glow': loading }"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* Define the gradient angle custom property with Safari fallback */
@property --gradient-angle {
  syntax: "<angle>";
  initial-value: 0turn;
  inherits: false;
}

/* Safari-specific fixes for iOS mobile */
@supports (-webkit-appearance: none) {
  .safari-ios-fix {
    /* Fallback for Safari that doesn't support CSS properties */
    --gradient-angle: 0deg;
  }
}

.circulate {
  position: relative;
  z-index: 10;
  transition: all 0.5s ease-in-out;
  width: auto;
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent; /* Đảm bảo nền trong suốt */
  /* Safari-specific fixes */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* Loading state styles for the container */
.circulate.loading-active {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
  z-index: 10001;
  transform: none;
  background: transparent; /* Đảm bảo nền trong suốt */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.circle {
  border-radius: 100% !important;
}

.circulate .circle {
  border-radius: 100% !important;
  background: white;
  /* Enhanced 3D shadow for crystal ball effect - scaled up */
  box-shadow:
    /* Main shadow */
    0 30px 80px rgba(0, 0, 0, 0.4),
    0 60px 160px rgba(0, 0, 0, 0.3),
    /* Colored glow */
    0 0 300px rgba(142, 45, 226, 0.3),
    /* Inner highlights */
    inset 0 0 60px rgba(255, 255, 255, 0.4),
    inset -20px -20px 40px rgba(0, 0, 0, 0.2),
    inset 20px 20px 40px rgba(255, 255, 255, 0.2),
    /* Depth shadow */
    0 0 0 8px rgba(255, 255, 255, 0.1),
    0 0 0 16px rgba(142, 45, 226, 0.1);
  background: linear-gradient(
    135deg,
    rgba(35, 52, 93, 0.95),
    rgba(146, 22, 100, 0.95),
    rgba(35, 52, 93, 0.9)
  );
  position: relative;
  overflow: hidden;
  /* Enhanced 3D transform */
  transform: perspective(2000px) rotateX(10deg) rotateY(-5deg);
  /* Safari-specific fixes for black square issue */
  -webkit-transform: perspective(2000px) rotateX(10deg) rotateY(-5deg);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  /* Prevent Safari rendering artifacts */
  isolation: isolate;
  /* Add border for more definition */
  border: 3px solid rgba(255, 255, 255, 0.2);
}

/* Adjust for light mode with Safari compatibility */
:root:not(.dark) .circulate .circle {
  box-shadow:
    /* Main shadow */
    0 25px 70px rgba(0, 0, 0, 0.3),
    0 50px 140px rgba(0, 0, 0, 0.2),
    /* Colored glow */
    0 0 250px rgba(142, 45, 226, 0.2),
    /* Inner highlights */
    inset 0 0 50px rgba(255, 255, 255, 0.5),
    inset -15px -15px 30px rgba(0, 0, 0, 0.15),
    inset 15px 15px 30px rgba(255, 255, 255, 0.3),
    /* Depth shadow */
    0 0 0 6px rgba(255, 255, 255, 0.15),
    0 0 0 12px rgba(142, 45, 226, 0.1);
  background: linear-gradient(
    135deg,
    rgba(35, 52, 93, 0.85),
    rgba(146, 22, 100, 0.85),
    rgba(35, 52, 93, 0.8)
  );
  /* Glass effect with Safari fallbacks */
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  /* Enhanced 3D transform - no hover animation */
  -webkit-transform: perspective(2000px) rotateX(10deg) rotateY(-5deg);
  transform: perspective(2000px) rotateX(10deg) rotateY(-5deg);
  /* Remove transitions */
  transition: none;
  /* Enhanced border */
  border: 3px solid rgba(255, 255, 255, 0.3);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      ellipse at 25% 25%,
      rgba(255, 255, 255, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%
    );
    border-radius: 100%;
    z-index: 2;
    pointer-events: none;
    /* Safari-specific fixes */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* Add secondary highlight */
  &::after {
    content: "";
    position: absolute;
    top: 10%;
    left: 10%;
    width: 30%;
    height: 30%;
    background: radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.6) 0%,
      rgba(255, 255, 255, 0.3) 40%,
      transparent 70%
    );
    border-radius: 100%;
    z-index: 3;
    pointer-events: none;
    filter: blur(1px);
    /* Safari-specific fixes */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* Gradient border animation when loading with Safari compatibility */
  &.loading-rotate {
    animation: 2s gradient-angle infinite linear;
    border: 2px solid transparent;
    /* Simplified background layers for Safari */
    background: linear-gradient(
      135deg,
      rgba(35, 52, 93, 0.9),
      rgba(146, 22, 100, 0.9)
    );
    /* Fallback border animation for Safari */
    /* Increase size slightly when loading - scaled up */
    width: 28rem !important; /* 7rem * 4 */
    height: 28rem !important; /* 7rem * 4 */
    -webkit-transform: perspective(1600px) rotateX(5deg) scale(1.1);
    transform: perspective(1600px) rotateX(5deg) scale(1.1);
  }
}

/* Safari-specific gradient border fallback */
@supports not (background-clip: border-box) {
  :root:not(.dark) .circulate .circle.loading-rotate {
    border: 2px solid #00cfff;
    animation: border-color-cycle 2s infinite linear;
  }
}

@keyframes border-color-cycle {
  0% {
    border-color: #00cfff;
  }
  25% {
    border-color: #a600ff;
  }
  50% {
    border-color: #ff006e;
  }
  75% {
    border-color: #ff8800;
  }
  100% {
    border-color: #00cfff;
  }
}

.circulate .wave {
  opacity: 0.6;
  position: absolute;
  top: 3%;
  left: 50%;
  background: rgba(15, 16, 35, 0.7);
  width: 800px; /* 200px * 4 */
  height: 800px; /* 200px * 4 */
  margin-left: -400px; /* -100px * 4 */
  margin-top: -620px; /* -155px * 4 */
  transform-origin: 50% 48%;
  border-radius: 43%;
  /* Remove animation, add static rotation */
  transform: rotate(45deg);
  /* Sharper waves */
  filter: blur(2px);
  /* Add 3D depth */
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
}

.circulate .wave._three {
  opacity: 0.7;
  filter: blur(3px);
  transform: rotate(135deg);
  background: rgba(15, 16, 35, 0.8);
}

.circulate .wave._two {
  opacity: 0.9;
  background: rgba(15, 16, 35, 0.9);
  filter: blur(1px);
  transform: rotate(225deg);
}

/* Loading state - add rotation animation only when loading */
.circulate .wave.loading-wave {
  opacity: 0.8;
  animation: humming 2s infinite linear;
}

.circulate .wave._three.loading-wave {
  opacity: 0.9;
  animation: humming 3s infinite linear;
}

.circulate .wave._two.loading-wave {
  opacity: 1;
  animation: humming 4s infinite linear;
}

.circulate .box:after {
  content: "";
  display: block;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 11;
  transform: translate3d(0, 0, 0);
}

/* Light reflection effect - scaled up and static */
.circulate .reflection {
  position: absolute;
  width: 35%;
  height: 20%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  border-radius: 50%;
  top: 12%;
  left: 12%;
  transform: rotate(-35deg);
  filter: blur(3px);
  z-index: 4;
  /* Add 3D depth */
  box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

/* Enhanced reflection when loading - just brighter */
.circulate .reflection.loading-reflection {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  filter: blur(2px);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.4);
}

.circulate .title {
  position: absolute;
  left: 0;
  top: 80px; /* 20px * 4 */
  width: 100%;
  z-index: 1;
  line-height: 120px; /* 30px * 4 */
  text-align: center;
  transform: translate3d(0, 0, 0);
  color: white;
  text-transform: uppercase;
  font-family: "Playfair Display", serif;
  letter-spacing: 1.6em; /* 0.4em * 4 */
  font-size: 96px; /* 24px * 4 */
  text-shadow: 0 4px 0 rgba(0, 0, 0, 0.1); /* 0 1px * 4 */
  text-indent: 1.2em; /* 0.3em * 4 */
}

@keyframes humming {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes moveReflection {
  0%,
  100% {
    opacity: 0.7;
    transform: rotate(-40deg) translateY(0);
  }
  50% {
    opacity: 0.9;
    transform: rotate(-35deg) translateY(20px); /* 5px * 4 */
  }
}

/* Image elements styles - scaled up and sharp */
.circulate .image-element {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow:
    0 0 20px rgba(255, 255, 255, 0.9),
    0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 2px rgba(255, 255, 255, 0.5);
  z-index: 5;
  opacity: 0.9;
  /* Remove blur for sharpness */
  filter: none;
  /* Add 3D transform */
  transform-style: preserve-3d;
}

.circulate .image-element.photo {
  width: 32px; /* Increased for better visibility */
  height: 32px;
  border-radius: 6px;
  background-size: cover;
  background-position: center;
  box-shadow:
    0 0 25px rgba(255, 255, 255, 0.8),
    0 6px 12px rgba(0, 0, 0, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.6),
    inset 0 -1px 2px rgba(0, 0, 0, 0.2);
  opacity: 0.95;
  /* Add border for definition */
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.circulate .image-element.photo::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  z-index: 3;
}

.circulate .image-element.photo1 {
  top: 28%;
  left: 22%;
  background: linear-gradient(
    135deg,
    #f5a623 0%,
    #f8e71c 50%,
    #f5a623 100%
  );
  /* Static 3D transform */
  transform: perspective(500px) rotateX(15deg) rotateY(-10deg) scale(0.9);
  /* Add depth shadow */
  box-shadow:
    0 0 25px rgba(245, 166, 35, 0.8),
    0 8px 16px rgba(0, 0, 0, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.6);
}

.circulate .image-element.photo2 {
  width: 28px;
  height: 40px;
  top: 58%;
  left: 62%;
  background: linear-gradient(
    135deg,
    #7ed321 0%,
    #b8e986 50%,
    #7ed321 100%
  );
  transform: perspective(500px) rotateX(-10deg) rotateY(15deg) scale(0.8);
  box-shadow:
    0 0 25px rgba(126, 211, 33, 0.8),
    0 8px 16px rgba(0, 0, 0, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.6);
}

.circulate .image-element.photo3 {
  width: 32px;
  height: 32px;
  top: 38%;
  left: 58%;
  background: linear-gradient(
    135deg,
    #bd10e0 0%,
    #d86eff 50%,
    #bd10e0 100%
  );
  transform: perspective(500px) rotateX(5deg) rotateY(-20deg) scale(0.85);
  box-shadow:
    0 0 25px rgba(189, 16, 224, 0.8),
    0 8px 16px rgba(0, 0, 0, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.6);
}

.circulate .image-element.photo4 {
  width: 48px;
  height: 36px;
  top: 22%;
  left: 48%;
  background: linear-gradient(
    135deg,
    #4a90e2 0%,
    #50e3c2 50%,
    #4a90e2 100%
  );
  transform: perspective(500px) rotateX(-5deg) rotateY(10deg) scale(0.75);
  box-shadow:
    0 0 25px rgba(74, 144, 226, 0.8),
    0 8px 16px rgba(0, 0, 0, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.6);
}

/* Particles styles - scaled up */
.circulate .particles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}

.circulate .particle {
  position: absolute;
  width: 8px; /* Smaller for subtlety */
  height: 8px;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(255, 255, 255, 0.2) 100%
  );
  border-radius: 50%;
  /* Sharp particles, no blur */
  filter: none;
  /* Add 3D depth */
  box-shadow:
    0 0 8px rgba(255, 255, 255, 0.8),
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 1px rgba(255, 255, 255, 0.9);
  border: 0.5px solid rgba(255, 255, 255, 0.4);
}

.circulate .particle.p1 {
  top: 18%;
  left: 28%;
  transform: perspective(300px) rotateX(10deg);
}
.circulate .particle.p2 {
  top: 72%;
  left: 42%;
  transform: perspective(300px) rotateX(-15deg);
}
.circulate .particle.p3 {
  top: 42%;
  left: 72%;
  transform: perspective(300px) rotateY(20deg);
}
.circulate .particle.p4 {
  top: 62%;
  left: 18%;
  transform: perspective(300px) rotateY(-10deg);
}
.circulate .particle.p5 {
  top: 32%;
  left: 62%;
  transform: perspective(300px) rotateX(5deg) rotateY(15deg);
}
.circulate .particle.p6 {
  top: 52%;
  left: 52%;
  transform: perspective(300px) rotateX(-8deg) rotateY(-12deg);
}
.circulate .particle.p7 {
  top: 77%;
  left: 67%;
  transform: perspective(300px) rotateX(12deg);
}
.circulate .particle.p8 {
  top: 37%;
  left: 33%;
  transform: perspective(300px) rotateY(-18deg);
}

/* Energy glow effect - static and sharp */
.circulate .energy-glow {
  position: absolute;
  width: 75%;
  height: 75%;
  top: 12.5%;
  left: 12.5%;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 223, 186, 0.2) 25%,
    rgba(186, 255, 201, 0.15) 50%,
    rgba(186, 225, 255, 0.1) 75%,
    transparent 100%
  );
  border-radius: 50%;
  z-index: 1;
  /* Remove animation */
  filter: blur(12px);
  opacity: 0.8;
  /* Safari-specific fixes */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Safari fallback without mix-blend-mode */
@supports not (mix-blend-mode: screen) {
  .circulate .energy-glow {
    opacity: 0.6;
  }
}

/* Add a second energy glow for more depth */
.circulate .energy-glow::after {
  content: "";
  position: absolute;
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
  background: radial-gradient(
    circle at center,
    rgba(255, 186, 186, 0.2) 0%,
    rgba(255, 186, 255, 0.15) 40%,
    rgba(186, 186, 255, 0.1) 70%,
    transparent 100%
  );
  border-radius: 50%;
  /* Remove animation */
  filter: blur(8px);
  opacity: 0.7;
  /* Safari-specific fixes */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Only keep gradient border animation for loading state */
@keyframes gradient-angle {
  to {
    --gradient-angle: 1turn;
  }
}

/* Keep humming animation for loading state only */
@keyframes humming {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
