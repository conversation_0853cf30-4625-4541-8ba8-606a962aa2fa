<script setup lang="ts">
const nuxtApp = useNuxtApp()
const { activeHeadings, updateHeadings } = useScrollspy()
const { t } = useI18n()
const items = computed(() => [
  {
    label: t('Generate'),
    to: '/app'
  },
  {
    label: t('History'),
    to: '/history'
  },
  {
    label: t('Pricing'),
    to: '/pricing'
  }
])

nuxtApp.hooks.hookOnce('page:finish', () => {
  updateHeadings(
    [
      document.querySelector('#features'),
      document.querySelector('#pricing'),
      document.querySelector('#testimonials')
    ].filter(Boolean) as Element[]
  )
})

const appStore = useAppStore()

const { loading } = storeToRefs(appStore)

const authStore = useAuthStore()
const { user, user_credit } = storeToRefs(authStore)
</script>

<template>
  <UHeader>
    <template #left>
      <NuxtLink to="/">
        <div class="flex flex-row gap-2 items-center mr-4">
          <BaseLargeLogo
            id="main-logo"
            :loading="loading"
            :size="`small`"
          />
          <BaseAppTitle
            class="justify-center text-center flex mx-auto !text-xl"
          />
        </div>
      </NuxtLink>
      <UNavigationMenu
        :items="items"
        variant="link"
        class="hidden lg:block"
      />
    </template>

    <template #right>
      <div class="flex flex-row gap-2 items-center">
        <div
          v-if="user"
          class="flex flex-row gap-4 items-center"
        >
          <!-- <UButton
            color="primary"
            variant="soft"
            trailing-icon="ic:baseline-plus"
            size="sm"
            class="hidden sm:block"
          >
            <div>
              {{ formatNumber(user_credit?.available_credit || 0) }}
              {{ $t("Credits") }}
            </div>
          </UButton> -->
          <BuyCreditsButton class="hidden sm:flex" />

          <NotificationBell />
          <AppUserMenu />
        </div>
        <div
          v-else
          class="flex flex-row gap-2 items-center"
        >
          <UButton
            :label="$t('Login')"
            variant="subtle"
            color="neutral"
            class="hidden lg:block"
            to="/auth/login"
          />
          <UButton
            :label="$t('ui.buttons.signUp')"
            variant="solid"
            class="hidden lg:block"
            to="/auth/signup"
          />
        </div>
      </div>
    </template>

    <template #body>
      <UNavigationMenu
        :items="items"
        orientation="vertical"
        class="-mx-2.5"
      />
      <BuyCreditsButton class="mt-4" />
    </template>
  </UHeader>
</template>
