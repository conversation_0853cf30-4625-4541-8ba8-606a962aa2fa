<template>
  <UCard
    :ui="{
      body: '!p-0'
    }"
  >
    <div class="px-5 pt-6 pb-0">
      <UTabs
        v-model="voiceTypeActive"
        :items="voiceTypes"
        class="w-full"
        color="neutral"
        :content="false"
        size="sm"
      />
    </div>
    <!-- Filter Controls -->
    <div class="px-5 py-3 border-b border-gray-200 dark:border-neutral-700">
      <div class="flex flex-col gap-3">
        <div class="flex flex-row gap-2 items-center">
          <!-- Search Input -->
          <UInput
            v-model="searchQuery"
            :placeholder="$t('Search...')"
            icon="i-heroicons-magnifying-glass-20-solid"
            size="sm"
            class="flex-1"
          >
            <template #trailing>
              <UButton
                v-show="searchQuery !== ''"
                color="neutral"
                variant="link"
                icon="i-heroicons-x-mark-20-solid"
                :padded="false"
                @click="searchQuery = ''"
              />
            </template>
          </UInput>
          <!-- Country/Accent Filter -->
          <USelectMenu
            v-model="selectedAccent"
            :items="accentOptions"
            placeholder="Country"
            value-key="value"
            size="sm"
            class="w-1/4 md:w-fit md:min-w-32"
            :ui="{
              content: 'w-52'
            }"
          />

          <!-- Gender Filter -->
          <USelectMenu
            v-model="selectedGender"
            :items="genderOptions"
            placeholder="Gender"
            value-key="value"
            size="sm"
            class="w-1/4 md:w-fit md:min-w-28"
          />

          <!-- Reset Button -->
          <UButton
            color="neutral"
            variant="outline"
            size="sm"
            icon="i-heroicons-arrow-path-20-solid"
            :disabled="!hasActiveFilters"
            @click="resetFilters"
          >
            <span class="hidden sm:inline">
              {{ $t("Reset") }}
            </span>
          </UButton>
        </div>
      </div>
    </div>
    <div
      ref="scrollContainer"
      class="overflow-y-auto thin-scrollbar relative"
      :class="{ 'h-[calc(100vh-170px)]': fullScreen, 'h-[64vh]': !fullScreen }"
      @scroll="handleScroll"
    >
      <!-- Sticky selected voice at top -->
      <div
        v-if="selectedVoice && shouldShowStickyTop"
        class="sticky -top-1 left-0 w-full px-5 py-2 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-neutral-700 z-10"
      >
        <VoiceCard
          :voice="selectedVoice"
          variant="soft"
          class="ring-1 ring-primary-500"
          mini
          :is-playing="playingVoices.has(selectedVoice.id)"
          @click="scrollToSelectedVoice"
          @play-preview="togglePlayPreview"
        />
      </div>
      <div v-if="loading">
        <VoiceCardLoading
          v-for="i in 10"
          :key="i"
        />
      </div>
      <div
        v-else-if="voicesFiltered.length > 0"
        class="px-5 py-2 flex flex-col gap-4"
      >
        <VoiceCard
          v-for="voice in voicesFiltered"
          :key="voice.id"
          :ref="(el) => setVoiceCardRef(voice.id, el)"
          :voice="voice"
          :class="{ 'ring-1 ring-primary-500': selectedVoice?.id === voice.id }"
          :is-playing="playingVoices.has(voice.id)"
          @click="selectVoice(voice)"
          @play-preview="togglePlayPreview"
        />
      </div>
      <div
        v-else
        class="flex flex-col items-center justify-center h-full text-neutral-500"
      >
        <UIcon
          name="ix:box-open"
          class="text-6xl mb-2"
        />
        <div class="text-sm">
          {{ $t("No voices found") }}
        </div>
      </div>
      <!-- Sticky selected voice at bottom -->
      <div
        v-if="selectedVoice && shouldShowStickyBottom"
        class="sticky -bottom-1 left-0 w-full px-5 py-2 bg-white/95 dark:bg-neutral-900/95 backdrop-blur-sm border-b border-gray-200 dark:border-neutral-700 z-10"
      >
        <VoiceCard
          :voice="selectedVoice"
          variant="soft"
          class="ring-1 ring-primary-500"
          mini
          :is-playing="playingVoices.has(selectedVoice.id)"
          @click="scrollToSelectedVoice"
          @play-preview="togglePlayPreview"
        />
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    fullScreen?: boolean
  }>(),
  {
    fullScreen: false
  }
)

const emits = defineEmits<{
  selectVoice: [voice: SpeechVoice]
}>()

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const { selectedVoice, loadVoices, voices, loading, voiceAccents }
  = useSpeechVoices()

// Filter states
const searchQuery = ref('')
const selectedAccent = ref<string | null>(null)
const selectedGender = ref<string | null>(null)

// Refs for sticky functionality
const scrollContainer = ref<HTMLElement>()
const voiceCardRefs = ref<Map<string, HTMLElement>>(new Map())
const isSelectedVoiceVisible = ref(true)
const selectedVoicePosition = ref<'top' | 'bottom' | 'visible'>('visible')
const playingVoices = ref<Set<string>>(new Set())
const currentAudio = ref<HTMLAudioElement | null>(null)

// Filter options
const accentOptions = computed(() => {
  const allOption = {
    value: null,
    label: t('All Countries'),
    icon: 'i-heroicons-globe-alt'
  }
  const accents = voiceAccents().map(accent => ({
    value: accent.value,
    label: accent.label,
    icon: accent.icon
  }))
  return [allOption, ...accents]
})

const genderOptions = computed(() => [
  { value: null, label: t('All Genders') },
  { value: 'male', label: t('Male') },
  { value: 'female', label: t('Female') }
])

const hasActiveFilters = computed(() => {
  return (
    searchQuery.value !== ''
    || selectedAccent.value !== null
    || selectedGender.value !== null
  )
})

const voiceTypes = computed(() => {
  return [
    {
      label: t('Gemini Voices'),
      value: 'gemini_voice',
      icon: 'ri:gemini-fill'
    },
    {
      label: t('System Voices'),
      value: 'system_voice',
      icon: 'hugeicons:voice'
    },
    {
      label: t('My Voices'),
      value: 'my_voices',
      icon: 'fluent:person-voice-24-filled'
    },
    {
      label: t('Favorite Voices'),
      value: 'favorite_voices',
      icon: 'mdi:puzzle-favorite'
    }
  ]
})

const voiceTypeActive = computed({
  get() {
    return (route.query.voiceType as string) || 'gemini_voice'
  },
  set(voiceType) {
    // Hash is specified here to prevent the page from scrolling to the top
    router.push({
      query: {
        ...route.query,
        voiceType
      }
    })
  }
})

const voicesFiltered = computed(() => {
  let filtered = voices.value

  // Filter by voice type
  if (voiceTypeActive.value === 'gemini_voice') {
    filtered = filtered.filter(voice => voice.type === 'gemini_voice')
  } else if (voiceTypeActive.value === 'system_voice') {
    filtered = filtered.filter(voice => voice.type === 'system_voice')
  } else if (voiceTypeActive.value === 'my_voices') {
    filtered = filtered.filter(voice => voice.type === 'user_voice')
  } else if (voiceTypeActive.value === 'favorite_voices') {
    filtered = filtered.filter(voice => voice.is_favorite)
  }

  // Filter by search query
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim()
    filtered = filtered.filter(
      voice =>
        voice.speaker_name.toLowerCase().includes(query)
        || voice.description.toLowerCase().includes(query)
    )
  }

  // Filter by accent/country
  if (selectedAccent.value) {
    filtered = filtered.filter(
      voice =>
        voice.accent.toLowerCase() === selectedAccent.value?.toLowerCase()
    )
  }

  // Filter by gender
  if (selectedGender.value) {
    filtered = filtered.filter(
      voice =>
        voice.gender.toLowerCase() === selectedGender.value?.toLowerCase()
    )
  }

  return filtered
})

// Computed properties for sticky visibility
const shouldShowStickyTop = computed(() => {
  return (
    selectedVoice.value
    && !isSelectedVoiceVisible.value
    && selectedVoicePosition.value === 'top'
    && selectedVoice.value.type === voiceTypeActive.value
  )
})

const shouldShowStickyBottom = computed(() => {
  return (
    selectedVoice.value
    && !isSelectedVoiceVisible.value
    && selectedVoicePosition.value === 'bottom'
    && selectedVoice.value.type === voiceTypeActive.value
  )
})

// Functions for sticky functionality
const setVoiceCardRef = (voiceId: string, el: any) => {
  if (el) {
    voiceCardRefs.value.set(voiceId, el.$el || el)
  } else {
    voiceCardRefs.value.delete(voiceId)
  }
}

const handleScroll = () => {
  if (!selectedVoice.value || !scrollContainer.value) return

  const selectedElement = voiceCardRefs.value.get(selectedVoice.value.id)
  if (!selectedElement) return

  const containerRect = scrollContainer.value.getBoundingClientRect()
  const elementRect = selectedElement.getBoundingClientRect()

  // Check if the selected voice card is visible in the scroll container
  const isVisible
    = elementRect.top >= containerRect.top
      && elementRect.bottom <= containerRect.bottom

  isSelectedVoiceVisible.value = isVisible

  if (!isVisible) {
    // Determine if the selected voice is above or below the visible area
    if (elementRect.top < containerRect.top) {
      selectedVoicePosition.value = 'top'
    } else {
      selectedVoicePosition.value = 'bottom'
    }
  } else {
    selectedVoicePosition.value = 'visible'
  }
}

const scrollToSelectedVoice = () => {
  if (!selectedVoice.value || !scrollContainer.value) return

  const selectedElement = voiceCardRefs.value.get(selectedVoice.value.id)
  if (!selectedElement) return

  selectedElement.scrollIntoView({
    behavior: 'smooth',
    block: 'center'
  })
}

const selectVoice = (voice: SpeechVoice) => {
  selectedVoice.value = voice
  emits('selectVoice', voice)
  // Check visibility after selection
  nextTick(() => {
    handleScroll()
  })
}

// Filter functions
const resetFilters = () => {
  searchQuery.value = ''
  selectedAccent.value = null
  selectedGender.value = null
}

onMounted(() => {
  loadVoices()
})

// Cleanup on unmount
onUnmounted(() => {
  stopAllAudio()
})

// Watch for voice type changes to reset scroll position
watch(voiceTypeActive, () => {
  nextTick(() => {
    if (scrollContainer.value) {
      scrollContainer.value.scrollTop = 0
    }
    handleScroll()
  })
})

// Audio functions
const stopAllAudio = () => {
  if (currentAudio.value) {
    currentAudio.value.pause()
    currentAudio.value = null
  }
  playingVoices.value.clear()
}

const togglePlayPreview = (voice: SpeechVoice) => {
  if (!voice.sample_audio_path) return

  // If this voice is currently playing, stop it
  if (playingVoices.value.has(voice.id)) {
    stopAllAudio()
    return
  }

  // Stop any currently playing audio
  stopAllAudio()

  try {
    const audio = new Audio(voice.sample_audio_path)
    currentAudio.value = audio
    playingVoices.value.add(voice.id)

    audio.addEventListener('ended', () => {
      playingVoices.value.delete(voice.id)
      if (currentAudio.value === audio) {
        currentAudio.value = null
      }
    })

    audio.addEventListener('error', () => {
      playingVoices.value.delete(voice.id)
      if (currentAudio.value === audio) {
        currentAudio.value = null
      }
    })

    audio.play().catch((err) => {
      console.error('Failed to play audio preview:', err)
      playingVoices.value.delete(voice.id)
      if (currentAudio.value === audio) {
        currentAudio.value = null
      }
    })
  } catch (err) {
    console.error('Failed to create audio element:', err)
    playingVoices.value.delete(voice.id)
  }
}
</script>
